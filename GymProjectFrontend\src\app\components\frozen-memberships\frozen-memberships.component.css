/* Frozen Memberships Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Content Blur Effect */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Toggle Button */
.toggle-button {
  background-color: var(--primary);
  color: white;
  padding: 12px 24px;
  transition: all 0.3s ease;
  width: 100%;
  max-width: none;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.toggle-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.toggle-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Card Styles */
.frozen-card, .history-card {
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.frozen-card:hover, .history-card:hover {
  transform: translateY(-5px);
}

/* Frozen Summary */
.frozen-summary {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 0.75rem 1.25rem;
  border-radius: var(--border-radius-md);
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
}

.summary-label {
  font-size: 0.85rem;
  color: var(--secondary);
}

/* Table Container */
.table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius-md);
  /* Responsive table için minimum genişlik */
  min-width: 100%;
  /* Scroll bar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) transparent;
}

/* Webkit scroll bar styling */
.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: var(--primary-light);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Dark mode scroll bar */
[data-theme="dark"] .table-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .table-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] .table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Scroll indicator */
.table-scroll-indicator {
  position: relative;
}

.table-scroll-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(to left, var(--bg-secondary) 0%, transparent 100%);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.table-scroll-indicator.has-scroll::after {
  opacity: 1;
}

/* Dark mode scroll indicator */
[data-theme="dark"] .table-scroll-indicator::after {
  background: linear-gradient(to left, var(--bg-secondary) 0%, transparent 100%);
}

/* Modern Table Responsive Enhancements */
.modern-table {
  min-width: 800px; /* Minimum genişlik belirle */
  white-space: nowrap; /* Metin sarmamasını engelle */
}

.modern-table th,
.modern-table td {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem; /* Biraz daha küçük font */
  vertical-align: middle;
}

/* Sütun genişlikleri */
.modern-table th:nth-child(1),
.modern-table td:nth-child(1) { /* Üye Adı */
  min-width: 180px;
  max-width: 200px;
}

.modern-table th:nth-child(2),
.modern-table td:nth-child(2) { /* Telefon */
  min-width: 120px;
  max-width: 140px;
}

.modern-table th:nth-child(3),
.modern-table td:nth-child(3) { /* Branş */
  min-width: 100px;
  max-width: 120px;
}

.modern-table th:nth-child(4),
.modern-table td:nth-child(4) { /* Başlangıç */
  min-width: 110px;
  max-width: 130px;
}

.modern-table th:nth-child(5),
.modern-table td:nth-child(5) { /* Bitiş */
  min-width: 110px;
  max-width: 130px;
}

.modern-table th:nth-child(6),
.modern-table td:nth-child(6) { /* Gerçek Bitiş / Kalan Gün */
  min-width: 120px;
  max-width: 140px;
}

.modern-table th:nth-child(7),
.modern-table td:nth-child(7) { /* Süre */
  min-width: 80px;
  max-width: 100px;
}

.modern-table th:nth-child(8),
.modern-table td:nth-child(8) { /* Kullanılan */
  min-width: 90px;
  max-width: 110px;
}

.modern-table th:nth-child(9),
.modern-table td:nth-child(9) { /* İşlem Türü */
  min-width: 120px;
  max-width: 140px;
}

.modern-table th:nth-child(10),
.modern-table td:nth-child(10) { /* İşlem Tarihi / İşlemler */
  min-width: 130px;
  max-width: 150px;
}

/* Tarih formatı için özel stil */
.date-cell {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Badge'ler için responsive boyut */
.modern-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  white-space: nowrap;
}

/* Member Name with Avatar */
.member-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Remaining Days Styling */
.remaining-days {
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

/* Modern Modal */
.modern-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modern-modal.show {
  opacity: 1;
  visibility: visible;
}

.modern-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.modern-modal-container {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.modern-modal.show .modern-modal-container {
  transform: translateY(0);
}

.modern-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modern-modal-title {
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: var(--secondary);
  transition: color 0.2s ease;
}

.modern-modal-close:hover {
  color: var(--danger);
}

.modern-modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  max-height: calc(90vh - 130px);
}

.modern-modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1.25rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  gap: 0.75rem;
}

/* Modern Search Input */
.search-container {
  position: relative;
  width: 300px;
}

.modern-search-input {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-search-input input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: none;
  border-radius: var(--border-radius-md);
  background-color: rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.modern-search-input input:focus {
  outline: none;
  background-color: rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--secondary);
  font-size: 0.95rem;
  pointer-events: none;
}

/* Animations */
.slide-in-left {
  animation: slideInLeft 0.5s forwards;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.zoom-in {
  transition: transform 0.3s ease;
}

.zoom-in:hover {
  transform: scale(1.02);
}

/* Dark Mode Support */
[data-theme="dark"] .summary-item {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-search-input input {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-search-input input:focus {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modern-modal-container {
  background-color: var(--background-dark);
}

[data-theme="dark"] .modern-modal-header, 
[data-theme="dark"] .modern-modal-footer {
  border-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 1199.98px) {
  /* Large screens - Tablet landscape */
  .modern-table {
    min-width: 900px;
  }

  .modern-table th,
  .modern-table td {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
  }
}

@media (max-width: 991.98px) {
  /* Medium screens - Tablet portrait */
  .modern-table {
    min-width: 1000px; /* Daha geniş tablo gerekli */
  }

  .modern-table th,
  .modern-table td {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
  }

  /* Sütun başlıklarını kısalt - İkonları gizle */
  .modern-table th i {
    display: none !important; /* İkonları gizle */
  }

  /* Responsive text gösterimi */
  .d-md-none {
    display: inline !important;
  }

  .d-none.d-md-inline {
    display: none !important;
  }
}

@media (max-width: 767.98px) {
  /* Small screens - Mobile */
  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .frozen-summary {
    width: 100%;
    margin-top: 1rem;
  }

  .search-container {
    width: 100%;
    margin-top: 1rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-buttons button {
    width: 100%;
    margin-left: 0 !important;
    margin-top: 0.25rem;
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }

  .action-buttons button:first-child {
    margin-top: 0;
  }

  .member-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .modern-table {
    min-width: 1100px; /* Mobile'da daha geniş tablo */
    font-size: 0.7rem;
  }

  .modern-table th,
  .modern-table td {
    padding: 0.25rem 0.375rem;
    font-size: 0.7rem;
  }

  /* Mobile'da tüm ikonları gizle */
  .modern-table th i {
    display: none !important;
  }

  /* Responsive text gösterimi - Mobile */
  .d-md-none {
    display: inline !important;
  }

  .d-none.d-md-inline {
    display: none !important;
  }

  /* Mobile'da tarih formatını kısalt */
  .date-cell {
    font-size: 0.65rem;
  }

  /* Badge'leri küçült */
  .modern-badge {
    font-size: 0.65rem;
    padding: 0.125rem 0.25rem;
  }

  /* Avatar'ı küçült */
  .modern-avatar {
    width: 24px !important;
    height: 24px !important;
    font-size: 0.7rem !important;
  }
}

@media (max-width: 575.98px) {
  /* Extra small screens */
  .modern-table {
    min-width: 1200px; /* En küçük ekranlarda en geniş tablo */
  }

  .modern-table th,
  .modern-table td {
    padding: 0.2rem 0.3rem;
    font-size: 0.65rem;
  }

  /* Sütun genişliklerini optimize et */
  .modern-table th:nth-child(1),
  .modern-table td:nth-child(1) { /* Üye Adı */
    min-width: 140px;
  }

  .modern-table th:nth-child(2),
  .modern-table td:nth-child(2) { /* Telefon */
    min-width: 100px;
  }

  .modern-table th:nth-child(3),
  .modern-table td:nth-child(3) { /* Branş */
    min-width: 80px;
  }

  .modern-table th:nth-child(4),
  .modern-table td:nth-child(4) { /* Başlangıç */
    min-width: 90px;
  }

  .modern-table th:nth-child(5),
  .modern-table td:nth-child(5) { /* Bitiş */
    min-width: 90px;
  }

  .modern-table th:nth-child(6),
  .modern-table td:nth-child(6) { /* Gerçek Bitiş / Kalan Gün */
    min-width: 100px;
  }

  .modern-table th:nth-child(7),
  .modern-table td:nth-child(7) { /* Süre */
    min-width: 70px;
  }

  .modern-table th:nth-child(8),
  .modern-table td:nth-child(8) { /* Kullanılan */
    min-width: 80px;
  }

  .modern-table th:nth-child(9),
  .modern-table td:nth-child(9) { /* İşlem Türü */
    min-width: 100px;
  }

  .modern-table th:nth-child(10),
  .modern-table td:nth-child(10) { /* İşlem Tarihi / İşlemler */
    min-width: 110px;
  }
}
